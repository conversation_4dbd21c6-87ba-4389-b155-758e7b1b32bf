package com.synthrexlabs.twgrow

import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.PixelFormat
import android.graphics.PorterDuff
import android.graphics.PorterDuffXfermode
import android.graphics.drawable.BitmapDrawable
import android.os.Build
import android.os.VibrationEffect
import android.os.Vibrator
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import io.flutter.plugin.common.MethodChannel

class OverlayService(private val context: Context) {
    private var windowManager: WindowManager? = null
    private var overlayView: View? = null
    private var isOverlayVisible = false
    private var methodChannel: MethodChannel? = null

    init {
        windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
    }

    fun setMethodChannel(channel: MethodChannel) {
        methodChannel = channel
    }

    fun showOverlay(current: Int, total: Int, progressText: String = "$current of $total channels", goBackText: String = "Go Back to App", nextChannelText: String = "Next Channel") {
        if (isOverlayVisible) {
            updateOverlay(current, total, progressText)
            return
        }

        try {
            // Create overlay view programmatically
            overlayView = createOverlayView(current, total, progressText, goBackText, nextChannelText)

            val layoutFlag = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            } else {
                @Suppress("DEPRECATION")
                WindowManager.LayoutParams.TYPE_PHONE
            }

            val params = WindowManager.LayoutParams(
                WindowManager.LayoutParams.WRAP_CONTENT,
                WindowManager.LayoutParams.WRAP_CONTENT,
                layoutFlag,
                WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                        WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL or
                        WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH,
                PixelFormat.TRANSLUCENT
            )

            params.gravity = Gravity.BOTTOM or Gravity.CENTER_HORIZONTAL
            params.y = 100 // 100px from bottom

            windowManager?.addView(overlayView, params)
            isOverlayVisible = true
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun hideOverlay() {
        try {
            if (isOverlayVisible && overlayView != null) {
                windowManager?.removeView(overlayView)
                isOverlayVisible = false
                overlayView = null
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun updateOverlay(current: Int, total: Int, progressText: String = "$current of $total channels") {
        overlayView?.findViewById<TextView>(R.id.progress_text)?.text = progressText
    }

    private fun createOverlayView(current: Int, total: Int, progressText: String, goBackText: String, nextChannelText: String): View {
        // Create a modern overlay layout with proper width constraints
        val overlayView = android.widget.LinearLayout(context).apply {
            orientation = android.widget.LinearLayout.VERTICAL
            setPadding(24, 20, 24, 20)

            // Set minimum width to ensure proper button layout
            minimumWidth = (280 * context.resources.displayMetrics.density).toInt()

            // Modern gradient background
            background = android.graphics.drawable.GradientDrawable().apply {
                val colors = intArrayOf(
                    android.graphics.Color.parseColor("#282842"),
                    android.graphics.Color.parseColor("#32324D")
                )
                setColors(colors)
                cornerRadius = 20f

                // Add shadow effect
                setStroke(1, android.graphics.Color.parseColor("#9146FF33"))
            }

            // Add elevation shadow
            elevation = 16f
        }

        // Header container with app icon on left, progress text in center, and close icon on right
        val headerContainer = android.widget.LinearLayout(context).apply {
            orientation = android.widget.LinearLayout.HORIZONTAL
            gravity = Gravity.CENTER_VERTICAL
            layoutParams = android.widget.LinearLayout.LayoutParams(
                android.widget.LinearLayout.LayoutParams.MATCH_PARENT,
                android.widget.LinearLayout.LayoutParams.WRAP_CONTENT
            )
            setPadding(0, 0, 0, 20)
        }

        // App icon with circular shape (left side)
        val appIcon = ImageView(context).apply {
            val iconSize = (32 * context.resources.displayMetrics.density).toInt()
            layoutParams = android.widget.LinearLayout.LayoutParams(iconSize, iconSize).apply {
                setMargins(0, 0, 0, 0)
            }

            // Set the app icon with circular shape
            try {
                val iconResourceId = context.resources.getIdentifier("app_icon", "drawable", context.packageName)
                if (iconResourceId != 0) {
                    val bitmap = BitmapFactory.decodeResource(context.resources, iconResourceId)
                    val circularBitmap = getCircularBitmap(bitmap)
                    setImageBitmap(circularBitmap)
                } else {
                    // Fallback to default app icon
                    setImageResource(android.R.drawable.sym_def_app_icon)
                }
            } catch (e: Exception) {
                // Fallback to default app icon
                setImageResource(android.R.drawable.sym_def_app_icon)
            }

            scaleType = ImageView.ScaleType.CENTER_CROP
        }

        // Progress text with modern styling (center)
        val progressTextView = TextView(context).apply {
            id = R.id.progress_text
            text = progressText
            textSize = 16f
            setTextColor(android.graphics.Color.WHITE)
            gravity = Gravity.CENTER
            typeface = android.graphics.Typeface.DEFAULT_BOLD
            layoutParams = android.widget.LinearLayout.LayoutParams(
                0,
                android.widget.LinearLayout.LayoutParams.WRAP_CONTENT,
                1f
            ).apply {
                setMargins(12, 0, 12, 0)
            }
        }

        // Close icon (right side)
        val closeIcon = ImageView(context).apply {
            val iconSize = (24 * context.resources.displayMetrics.density).toInt()
            layoutParams = android.widget.LinearLayout.LayoutParams(iconSize, iconSize)

            // Create close icon programmatically
            background = android.graphics.drawable.GradientDrawable().apply {
                shape = android.graphics.drawable.GradientDrawable.OVAL
                setColor(android.graphics.Color.parseColor("#FF4444"))
            }

            // Set close icon (X)
            setImageResource(android.R.drawable.ic_menu_close_clear_cancel)
            scaleType = ImageView.ScaleType.CENTER
            setPadding(4, 4, 4, 4)

            setOnClickListener {
                // Add haptic feedback
                performHapticFeedback()
                // Hide overlay completely
                hideOverlay()
                // Send close message to Flutter
                sendMessageToFlutter("close")
            }
        }

        // Button container with modern styling and proper width
        val buttonContainer = android.widget.LinearLayout(context).apply {
            orientation = android.widget.LinearLayout.HORIZONTAL
            gravity = Gravity.CENTER
            layoutParams = android.widget.LinearLayout.LayoutParams(
                android.widget.LinearLayout.LayoutParams.MATCH_PARENT,
                android.widget.LinearLayout.LayoutParams.WRAP_CONTENT
            )
        }

        // Modern Go Back button
        val goBackButton = Button(context).apply {
            text = goBackText
            textSize = 14f
            setTextColor(android.graphics.Color.WHITE)
            typeface = android.graphics.Typeface.DEFAULT_BOLD

            // Modern button background with gradient
            background = android.graphics.drawable.GradientDrawable().apply {
                val colors = intArrayOf(
                    android.graphics.Color.parseColor("#9146FF"),
                    android.graphics.Color.parseColor("#7C3AFA")
                )
                setColors(colors)
                cornerRadius = 16f
            }

            setPadding(32, 16, 32, 16)

            val params = android.widget.LinearLayout.LayoutParams(
                0,
                android.widget.LinearLayout.LayoutParams.WRAP_CONTENT,
                1f
            )
            params.setMargins(0, 0, 6, 0)
            layoutParams = params

            setOnClickListener {
                // Add haptic feedback
                performHapticFeedback()
                // Bring app to foreground and send message to Flutter
                bringAppToForeground()
                sendMessageToFlutter("goBack")
            }
        }

        // Modern Next Channel button
        val nextButton = Button(context).apply {
            text = nextChannelText
            textSize = 14f
            setTextColor(android.graphics.Color.WHITE)
            typeface = android.graphics.Typeface.DEFAULT_BOLD

            // Modern button background with gradient
            background = android.graphics.drawable.GradientDrawable().apply {
                val colors = intArrayOf(
                    android.graphics.Color.parseColor("#9146FF"),
                    android.graphics.Color.parseColor("#7C3AFA")
                )
                setColors(colors)
                cornerRadius = 16f
            }

            setPadding(32, 16, 32, 16)

            val params = android.widget.LinearLayout.LayoutParams(
                0,
                android.widget.LinearLayout.LayoutParams.WRAP_CONTENT,
                1f
            )
            params.setMargins(6, 0, 0, 0)
            layoutParams = params

            setOnClickListener {
                // Add haptic feedback
                performHapticFeedback()
                // Send message back to Flutter
                sendMessageToFlutter("nextChannel")
            }
        }

        buttonContainer.addView(goBackButton)
        buttonContainer.addView(nextButton)

        // Add icon, text, and close button to header container
        headerContainer.addView(appIcon)
        headerContainer.addView(progressTextView)
        headerContainer.addView(closeIcon)

        // Add header and buttons to main overlay
        overlayView.addView(headerContainer)
        overlayView.addView(buttonContainer)

        return overlayView
    }

    private fun bringAppToForeground() {
        try {
            val packageManager = context.packageManager
            val intent = packageManager.getLaunchIntentForPackage(context.packageName)
            intent?.let {
                it.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_SINGLE_TOP)
                context.startActivity(it)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun performHapticFeedback() {
        try {
            val vibrator = context.getSystemService(Context.VIBRATOR_SERVICE) as Vibrator
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                vibrator.vibrate(VibrationEffect.createOneShot(50, VibrationEffect.DEFAULT_AMPLITUDE))
            } else {
                @Suppress("DEPRECATION")
                vibrator.vibrate(50)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun getCircularBitmap(bitmap: Bitmap): Bitmap {
        val size = minOf(bitmap.width, bitmap.height)
        val output = Bitmap.createBitmap(size, size, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(output)

        val paint = Paint().apply {
            isAntiAlias = true
            color = android.graphics.Color.RED
        }

        // Draw circle
        canvas.drawCircle(size / 2f, size / 2f, size / 2f, paint)

        // Apply source bitmap with porter duff mode
        paint.xfermode = PorterDuffXfermode(PorterDuff.Mode.SRC_IN)

        // Scale and center the bitmap
        val left = (size - bitmap.width) / 2f
        val top = (size - bitmap.height) / 2f
        canvas.drawBitmap(bitmap, left, top, paint)

        return output
    }

    private fun sendMessageToFlutter(action: String) {
        methodChannel?.invokeMethod("overlayAction", mapOf("action" to action))
    }
}

// Define IDs for views
object R {
    object id {
        const val progress_text = 1001
    }
}
