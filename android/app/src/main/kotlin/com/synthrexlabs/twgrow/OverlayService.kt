package com.synthrexlabs.twgrow

import android.content.Context
import android.content.Intent
import android.graphics.PixelFormat
import android.os.Build
import android.os.VibrationEffect
import android.os.Vibrator
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import android.widget.Button
import android.widget.TextView
import io.flutter.plugin.common.MethodChannel

class OverlayService(private val context: Context) {
    private var windowManager: WindowManager? = null
    private var overlayView: View? = null
    private var isOverlayVisible = false
    private var methodChannel: MethodChannel? = null

    init {
        windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
    }

    fun setMethodChannel(channel: MethodChannel) {
        methodChannel = channel
    }

    fun showOverlay(current: Int, total: Int, progressText: String = "$current of $total channels", goBackText: String = "Go Back to App", nextChannelText: String = "Next Channel") {
        if (isOverlayVisible) {
            updateOverlay(current, total, progressText)
            return
        }

        try {
            // Create overlay view programmatically
            overlayView = createOverlayView(current, total, progressText, goBackText, nextChannelText)

            val layoutFlag = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            } else {
                @Suppress("DEPRECATION")
                WindowManager.LayoutParams.TYPE_PHONE
            }

            val params = WindowManager.LayoutParams(
                WindowManager.LayoutParams.WRAP_CONTENT,
                WindowManager.LayoutParams.WRAP_CONTENT,
                layoutFlag,
                WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                        WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL or
                        WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH,
                PixelFormat.TRANSLUCENT
            )

            params.gravity = Gravity.BOTTOM or Gravity.CENTER_HORIZONTAL
            params.y = 100 // 100px from bottom

            windowManager?.addView(overlayView, params)
            isOverlayVisible = true
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun hideOverlay() {
        try {
            if (isOverlayVisible && overlayView != null) {
                windowManager?.removeView(overlayView)
                isOverlayVisible = false
                overlayView = null
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun updateOverlay(current: Int, total: Int, progressText: String = "$current of $total channels") {
        overlayView?.findViewById<TextView>(R.id.progress_text)?.text = progressText
    }

    private fun createOverlayView(current: Int, total: Int, progressText: String, goBackText: String, nextChannelText: String): View {
        // Create a modern overlay layout
        val overlayView = android.widget.LinearLayout(context).apply {
            orientation = android.widget.LinearLayout.VERTICAL
            setPadding(24, 20, 24, 20)

            // Modern gradient background
            background = android.graphics.drawable.GradientDrawable().apply {
                val colors = intArrayOf(
                    android.graphics.Color.parseColor("#282842"),
                    android.graphics.Color.parseColor("#32324D")
                )
                setColors(colors)
                cornerRadius = 20f

                // Add shadow effect
                setStroke(1, android.graphics.Color.parseColor("#9146FF33"))
            }

            // Add elevation shadow
            elevation = 16f
        }

        // Progress text with modern styling
        val progressTextView = TextView(context).apply {
            id = R.id.progress_text
            text = progressText
            textSize = 16f
            setTextColor(android.graphics.Color.WHITE)
            gravity = Gravity.CENTER
            setPadding(0, 8, 0, 20)
            typeface = android.graphics.Typeface.DEFAULT_BOLD
        }

        // Button container with modern styling
        val buttonContainer = android.widget.LinearLayout(context).apply {
            orientation = android.widget.LinearLayout.HORIZONTAL
            gravity = Gravity.CENTER
        }

        // Modern Go Back button
        val goBackButton = Button(context).apply {
            text = goBackText
            textSize = 14f
            setTextColor(android.graphics.Color.WHITE)
            typeface = android.graphics.Typeface.DEFAULT_BOLD

            // Modern button background with gradient
            background = android.graphics.drawable.GradientDrawable().apply {
                val colors = intArrayOf(
                    android.graphics.Color.parseColor("#9146FF"),
                    android.graphics.Color.parseColor("#7C3AFA")
                )
                setColors(colors)
                cornerRadius = 16f
            }

            setPadding(32, 16, 32, 16)

            val params = android.widget.LinearLayout.LayoutParams(
                android.widget.LinearLayout.LayoutParams.WRAP_CONTENT,
                android.widget.LinearLayout.LayoutParams.WRAP_CONTENT
            )
            params.setMargins(0, 0, 12, 0)
            layoutParams = params

            setOnClickListener {
                // Add haptic feedback
                performHapticFeedback()
                // Bring app to foreground and send message to Flutter
                bringAppToForeground()
                sendMessageToFlutter("goBack")
            }
        }

        // Modern Next Channel button
        val nextButton = Button(context).apply {
            text = nextChannelText
            textSize = 14f
            setTextColor(android.graphics.Color.WHITE)
            typeface = android.graphics.Typeface.DEFAULT_BOLD

            // Modern button background with gradient
            background = android.graphics.drawable.GradientDrawable().apply {
                val colors = intArrayOf(
                    android.graphics.Color.parseColor("#9146FF"),
                    android.graphics.Color.parseColor("#7C3AFA")
                )
                setColors(colors)
                cornerRadius = 16f
            }

            setPadding(32, 16, 32, 16)

            setOnClickListener {
                // Add haptic feedback
                performHapticFeedback()
                // Send message back to Flutter
                sendMessageToFlutter("nextChannel")
            }
        }

        buttonContainer.addView(goBackButton)
        buttonContainer.addView(nextButton)

        overlayView.addView(progressTextView)
        overlayView.addView(buttonContainer)

        return overlayView
    }

    private fun bringAppToForeground() {
        try {
            val packageManager = context.packageManager
            val intent = packageManager.getLaunchIntentForPackage(context.packageName)
            intent?.let {
                it.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_SINGLE_TOP)
                context.startActivity(it)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun performHapticFeedback() {
        try {
            val vibrator = context.getSystemService(Context.VIBRATOR_SERVICE) as Vibrator
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                vibrator.vibrate(VibrationEffect.createOneShot(50, VibrationEffect.DEFAULT_AMPLITUDE))
            } else {
                @Suppress("DEPRECATION")
                vibrator.vibrate(50)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun sendMessageToFlutter(action: String) {
        methodChannel?.invokeMethod("overlayAction", mapOf("action" to action))
    }
}

// Define IDs for views
object R {
    object id {
        const val progress_text = 1001
    }
}
