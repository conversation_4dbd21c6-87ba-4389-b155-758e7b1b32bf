package com.synthrexlabs.twgrow

import android.content.Context
import android.content.Intent
import android.graphics.PixelFormat
import android.os.Build
import android.os.VibrationEffect
import android.os.Vibrator
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import android.widget.Button
import android.widget.TextView
import io.flutter.plugin.common.MethodChannel

class OverlayService(private val context: Context) {
    private var windowManager: WindowManager? = null
    private var overlayView: View? = null
    private var isOverlayVisible = false
    private var methodChannel: MethodChannel? = null

    init {
        windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
    }

    fun setMethodChannel(channel: MethodChannel) {
        methodChannel = channel
    }

    fun showOverlay(current: Int, total: Int) {
        if (isOverlayVisible) {
            updateOverlay(current, total)
            return
        }

        try {
            // Create overlay view programmatically
            overlayView = createOverlayView(current, total)

            val layoutFlag = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            } else {
                @Suppress("DEPRECATION")
                WindowManager.LayoutParams.TYPE_PHONE
            }

            val params = WindowManager.LayoutParams(
                WindowManager.LayoutParams.WRAP_CONTENT,
                WindowManager.LayoutParams.WRAP_CONTENT,
                layoutFlag,
                WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                        WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL or
                        WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH,
                PixelFormat.TRANSLUCENT
            )

            params.gravity = Gravity.BOTTOM or Gravity.CENTER_HORIZONTAL
            params.y = 100 // 100px from bottom

            windowManager?.addView(overlayView, params)
            isOverlayVisible = true
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun hideOverlay() {
        try {
            if (isOverlayVisible && overlayView != null) {
                windowManager?.removeView(overlayView)
                isOverlayVisible = false
                overlayView = null
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun updateOverlay(current: Int, total: Int) {
        overlayView?.findViewById<TextView>(R.id.progress_text)?.text = "$current of $total channels"
    }

    private fun createOverlayView(current: Int, total: Int): View {
        val inflater = LayoutInflater.from(context)
        
        // Create a simple linear layout programmatically since we don't have XML layouts
        val overlayView = android.widget.LinearLayout(context).apply {
            orientation = android.widget.LinearLayout.VERTICAL
            setPadding(32, 24, 32, 24)
            setBackgroundColor(android.graphics.Color.parseColor("#1F1F2E"))
            
            // Set rounded corners
            background = android.graphics.drawable.GradientDrawable().apply {
                setColor(android.graphics.Color.parseColor("#1F1F2E"))
                cornerRadius = 24f
            }
        }

        // Progress text
        val progressText = TextView(context).apply {
            id = R.id.progress_text
            text = "$current of $total channels"
            textSize = 16f
            setTextColor(android.graphics.Color.WHITE)
            gravity = Gravity.CENTER
            setPadding(0, 0, 0, 16)
        }

        // Button container
        val buttonContainer = android.widget.LinearLayout(context).apply {
            orientation = android.widget.LinearLayout.HORIZONTAL
            gravity = Gravity.CENTER
        }

        // Go Back button
        val goBackButton = Button(context).apply {
            text = "Go Back"
            textSize = 14f
            setTextColor(android.graphics.Color.WHITE)
            setBackgroundColor(android.graphics.Color.parseColor("#9146FF"))
            setPadding(24, 12, 24, 12)
            
            val params = android.widget.LinearLayout.LayoutParams(
                android.widget.LinearLayout.LayoutParams.WRAP_CONTENT,
                android.widget.LinearLayout.LayoutParams.WRAP_CONTENT
            )
            params.setMargins(0, 0, 16, 0)
            layoutParams = params

            setOnClickListener {
                // Add haptic feedback
                performHapticFeedback()
                // Bring app to foreground and send message to Flutter
                bringAppToForeground()
                sendMessageToFlutter("goBack")
            }
        }

        // Next Channel button
        val nextButton = Button(context).apply {
            text = "Next Channel"
            textSize = 14f
            setTextColor(android.graphics.Color.WHITE)
            setBackgroundColor(android.graphics.Color.parseColor("#9146FF"))
            setPadding(24, 12, 24, 12)

            setOnClickListener {
                // Add haptic feedback
                performHapticFeedback()
                // Send message back to Flutter
                sendMessageToFlutter("nextChannel")
            }
        }

        buttonContainer.addView(goBackButton)
        buttonContainer.addView(nextButton)

        overlayView.addView(progressText)
        overlayView.addView(buttonContainer)

        return overlayView
    }

    private fun bringAppToForeground() {
        try {
            val packageManager = context.packageManager
            val intent = packageManager.getLaunchIntentForPackage(context.packageName)
            intent?.let {
                it.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_SINGLE_TOP)
                context.startActivity(it)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun performHapticFeedback() {
        try {
            val vibrator = context.getSystemService(Context.VIBRATOR_SERVICE) as Vibrator
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                vibrator.vibrate(VibrationEffect.createOneShot(50, VibrationEffect.DEFAULT_AMPLITUDE))
            } else {
                @Suppress("DEPRECATION")
                vibrator.vibrate(50)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun sendMessageToFlutter(action: String) {
        methodChannel?.invokeMethod("overlayAction", mapOf("action" to action))
    }
}

// Define IDs for views
object R {
    object id {
        const val progress_text = 1001
    }
}
