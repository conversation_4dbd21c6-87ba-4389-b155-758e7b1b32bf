
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:twitch/provider.dart';
import 'package:twitch/twitch_client.dart';
import 'package:twitch/twitch_login.dart';
import 'package:twitch/rate.dart';
import 'package:twitch/overlay_service.dart';

class SimplifiedF4FScreen extends StatefulWidget {
  const SimplifiedF4FScreen({super.key});

  @override
  SimplifiedF4FScreenState createState() => SimplifiedF4FScreenState();
}

class SimplifiedF4FScreenState extends State<SimplifiedF4FScreen>
    with WidgetsBindingObserver {
  final PageController _pageController = PageController();
  bool _isFollowing = false;
  bool _isAddingCoins = false;
  late TwitchClient _twitchClient;
  int? _pendingFollowIndex;
  bool _isClaimingBonus = false;
  bool _showWelcomeBonus = false;
  bool _autoFollowEnabled = false;
  bool _showAutoFollowWidget = false;
  int _currentAutoFollowIndex = 0;
  bool _isAutoFollowActive = false;

  bool _showFollowBonusWidget = false;
  int _followBonusProgress = 0;
  bool _isClaimingDailyBonus = false;
  bool _isCheckingVisitedChannels = false;
  List<String> _visitedChannels = [];

  final List<Map<String, dynamic>> _streamers = [];
  List<String> following = [];
  bool firstFollow = true; // Reset each app session
  final OverlayService _overlayService = OverlayService();
  List<String> _unfollowedStreamers = [];

  @override
  void initState() {
    initFollowing();
    getStreamers();
    _checkWelcomeBonus();
    _checkAutoFollowWidget();
    _checkBonuses();
    _setupOverlayCallbacks();
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _initTwitchClient();

    // Check visited channels after a short delay to ensure everything is initialized
    Future.delayed(const Duration(milliseconds: 500), () {
      _checkVisitedChannels();
    });
  }

  void _setupOverlayCallbacks() {
    _overlayService.onOverlayAction = (String action) {
      switch (action) {
        case 'goBack':
          _handleOverlayGoBack();
          break;
        case 'nextChannel':
          _handleOverlayNextChannel();
          break;
        case 'close':
          _handleOverlayClose();
          break;
      }
    };
  }

  void _handleOverlayGoBack() {
    // Hide overlay and stop autofollow
    _overlayService.hideOverlay();
    setState(() {
      _autoFollowEnabled = false;
      _isAutoFollowActive = false;
    });
  }

  void _handleOverlayNextChannel() {
    // Move to next channel
    _currentAutoFollowIndex++;
    _openNextChannel();
  }

  void _handleOverlayClose() {
    // Close overlay and stop autofollow without returning to app
    setState(() {
      _autoFollowEnabled = false;
      _isAutoFollowActive = false;
    });
  }

  void _openNextChannel() async {
    if (_currentAutoFollowIndex >= _unfollowedStreamers.length) {
      // No more channels, stop autofollow
      _handleOverlayGoBack();
      return;
    }

    final username = _unfollowedStreamers[_currentAutoFollowIndex];

    // Store visited channel
    await _overlayService.storeVisitedChannel(username);

    // Update overlay with current progress in simple format
    final progressText = '${_currentAutoFollowIndex + 1}/${_unfollowedStreamers.length}';

    await _overlayService.showOverlay(
      _currentAutoFollowIndex + 1,
      _unfollowedStreamers.length,
      progressText: progressText,
      goBackText: 'overlay.go_back_to_app'.tr(),
      nextChannelText: 'overlay.next_channel'.tr(),
    );

    // Open channel
    await _overlayService.openTwitchChannel(username);
  }

  final followBox = Hive.box("Follow");
  void initFollowing() {
    following = followBox.get("Follow") ?? [];
  }

  void _checkWelcomeBonus() {
    final bool bonusClaimed = loginBox.get("welcomeBonusClaimed") ?? false;
    setState(() {
      _showWelcomeBonus = !bonusClaimed;
    });
  }

  void _checkAutoFollowWidget() {
    // Check if user is logged in with Twitch
    String? token = loginBox.get("token");
    bool isTwitchLoggedIn = token != null && token.isNotEmpty;

    if (isTwitchLoggedIn) {
      // Auto follow is always disabled when app starts
      setState(() {
        _showAutoFollowWidget = true;
        _autoFollowEnabled = false;
      });
    } else {
      setState(() {
        _showAutoFollowWidget = false;
        _autoFollowEnabled = false;
      });
    }
  }

  void _checkBonuses() async {
    try {
      String uid = loginBox.get("UID") ?? "";
      if (uid.isEmpty) return;

      final response =
          await Dio().get("https://synthrexlabs.com/twgrow/bonus/$uid");

      if (response.statusCode == 200 && response.data["status"] == "Success") {
        final bonuses = response.data["bonuses"];

        // Check daily bonus - available for all users
        if (bonuses["DailyBonusCompleted"] == false) {
          _claimDailyBonus();
        }

        // Check follow bonus - only for Twitch logged users
        String? token = loginBox.get("token");
        bool isTwitchLoggedIn = token != null && token.isNotEmpty;

        if (isTwitchLoggedIn) {
          if (bonuses["FollowBonusCompleted"] == false) {
            // Get current follow count from storage (separate from following list)
            final int currentFollowCount =
                loginBox.get("followBonusCount") ?? 0;
            setState(() {
              _showFollowBonusWidget = true;
              _followBonusProgress =
                  currentFollowCount % 30; // Progress towards next bonus
            });
          } else {
            setState(() {
              _showFollowBonusWidget = false;
            });
          }
        } else {
          setState(() {
            _showFollowBonusWidget = false;
          });
        }
      }
    } catch (e) {
      debugPrint('Error checking bonuses: $e');
    }
  }

  void _checkVisitedChannels() async {
    try {
      // Get visited channels from native storage
      final visitedChannels = await _overlayService.getVisitedChannels();

      if (visitedChannels.isNotEmpty) {
        setState(() {
          _isCheckingVisitedChannels = true;
          _visitedChannels = visitedChannels;
        });

        // Check follows for visited channels and grant coins
        await _processVisitedChannels(visitedChannels);

        // Clear visited channels after processing
        await _overlayService.clearVisitedChannels();

        setState(() {
          _isCheckingVisitedChannels = false;
          _visitedChannels = [];
        });
      }
    } catch (e) {
      debugPrint('Error checking visited channels: $e');
      setState(() {
        _isCheckingVisitedChannels = false;
      });
    }
  }

  void _checkFollowBonusForTwitchUser() async {
    try {
      String uid = loginBox.get("UID") ?? "";
      if (uid.isEmpty) return;

      final response =
          await Dio().get("https://synthrexlabs.com/twgrow/bonus/$uid");

      if (response.statusCode == 200 && response.data["status"] == "Success") {
        final bonuses = response.data["bonuses"];

        // Check follow bonus for newly logged in Twitch user
        if (bonuses["FollowBonusCompleted"] == false) {
          final int currentFollowCount = loginBox.get("followBonusCount") ?? 0;
          setState(() {
            _showFollowBonusWidget = true;
            _followBonusProgress =
                currentFollowCount % 30; // Progress towards next bonus
          });
        }
      }
    } catch (e) {
      debugPrint('Error checking follow bonus for Twitch user: $e');
    }
  }

  Future<void> _claimDailyBonus() async {
    if (_isClaimingDailyBonus) return;

    setState(() {
      _isClaimingDailyBonus = true;
    });

    try {
      String uid = loginBox.get("UID") ?? "";
      final provider = Provider.of<Data>(context, listen: false);

      // Add 50 coins for daily bonus
      await provider.addPoints(50);

      // Mark daily bonus as completed
      await Dio()
          .get("https://synthrexlabs.com/twgrow/completedDailyBonus/$uid");

      // Show animated dialog
      if (mounted) {
        _showDailyBonusDialog();
      }
    } catch (e) {
      debugPrint('Error claiming daily bonus: $e');
    } finally {
      setState(() {
        _isClaimingDailyBonus = false;
      });
    }
  }

  void _showDailyBonusDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [Color(0xFF282842), Color(0xFF32324D)],
            ),
            borderRadius: BorderRadius.circular(24),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.4),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Padding(
            padding: const EdgeInsets.all(28),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Animated coin icon
                TweenAnimationBuilder(
                  duration: const Duration(milliseconds: 800),
                  tween: Tween<double>(begin: 0.0, end: 1.0),
                  builder: (context, double value, child) {
                    return Transform.scale(
                      scale: value,
                      child: Container(
                        width: 80,
                        height: 80,
                        decoration: BoxDecoration(
                          gradient: const LinearGradient(
                            colors: [Color(0xFF9146FF), Color(0xFF7C3AFA)],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                          borderRadius: BorderRadius.circular(20),
                          boxShadow: [
                            BoxShadow(
                              color: const Color(0xFF9146FF)
                                  .withValues(alpha: 0.4),
                              blurRadius: 16,
                              offset: const Offset(0, 8),
                            ),
                          ],
                        ),
                        child: const Icon(
                          Icons.monetization_on,
                          size: 40,
                          color: Colors.white,
                        ),
                      ),
                    );
                  },
                ),
                const SizedBox(height: 24),
                Text(
                  'daily_bonus.title'.tr(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 12),
                Text(
                  'daily_bonus.description'.tr(args: ['50']),
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.8),
                    fontSize: 16,
                    height: 1.4,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                Container(
                  width: double.infinity,
                  height: 52,
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      colors: [Color(0xFF9146FF), Color(0xFF7C3AFA)],
                      begin: Alignment.centerLeft,
                      end: Alignment.centerRight,
                    ),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      onTap: () => Navigator.pop(context),
                      borderRadius: BorderRadius.circular(16),
                      child: Center(
                        child: Text(
                          'daily_bonus.claim_button'.tr(),
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _updateFollowBonusProgress() async {
    if (!_showFollowBonusWidget) return;

    // Increment the follow bonus counter
    final int currentFollowCount = loginBox.get("followBonusCount") ?? 0;
    final int newFollowCount = currentFollowCount + 1;
    await loginBox.put("followBonusCount", newFollowCount);

    setState(() {
      _followBonusProgress = newFollowCount % 30;
    });

    // Check if bonus is completed (30 follows)
    if (newFollowCount % 30 == 0) {
      await _completeFollowBonus();
    }
  }

  Future<void> _completeFollowBonus() async {
    try {
      String uid = loginBox.get("UID") ?? "";
      final provider = Provider.of<Data>(context, listen: false);

      // Add 100 coins for follow bonus
      await provider.addPoints(100);

      // Mark follow bonus as completed
      await Dio()
          .get("https://synthrexlabs.com/twgrow/completedFollowBonus/$uid");

      // Reset the follow bonus counter
      await loginBox.put("followBonusCount", 0);

      // Show completion animation and hide widget
      if (mounted) {
        _showFollowBonusCompletionAnimation();
      }
    } catch (e) {
      debugPrint('Error completing follow bonus: $e');
    }
  }

  void _showFollowBonusCompletionAnimation() {
    // Hide the widget instantly
    setState(() {
      _showFollowBonusWidget = false;
    });

    // Show success snackbar
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.celebration, color: Color(0xFF9146FF)),
            const SizedBox(width: 8),
            Expanded(child: Text('follow_bonus.completed'.tr(args: ['100']))),
          ],
        ),
        backgroundColor: const Color(0xFF282842),
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }

  void _toggleAutoFollow(bool value) async {
    if (value) {
      // Check overlay permission before enabling auto follow
      final hasPermission = await _overlayService.checkOverlayPermission();
      if (!hasPermission) {
        _showOverlayPermissionDialog();
        return;
      }

      setState(() {
        _autoFollowEnabled = value;
      });

      // Start auto follow process
      _startAutoFollow();
    } else {
      setState(() {
        _autoFollowEnabled = value;
      });

      // Hide overlay and stop auto follow process
      await _overlayService.hideOverlay();
      _stopAutoFollow();
      // Navigate to next unfollowed streamer
      _navigateToNextUnfollowed();
    }
  }

  void _startAutoFollow() {
    if (_streamers.isEmpty) return;

    // Get list of unfollowed streamers
    _unfollowedStreamers = _streamers
        .where((streamer) => !following.contains(streamer['username']))
        .map((streamer) => streamer['username'] as String)
        .toList();

    if (_unfollowedStreamers.isEmpty) return;

    setState(() {
      _isAutoFollowActive = true;
      _currentAutoFollowIndex = 0;
    });

    // Show overlay and open first channel
    _openNextChannel();
  }

  void _stopAutoFollow() {
    setState(() {
      _isAutoFollowActive = false;
      _pendingFollowIndex = null;
    });
  }

  void _navigateToNextUnfollowed() {
    if (_streamers.isEmpty) return;

    // Find the next unfollowed streamer starting from current position
    int targetIndex = -1;

    // First, try to find an unfollowed streamer from the current position onwards
    for (int i = _currentAutoFollowIndex; i < _streamers.length; i++) {
      if (!following.contains(_streamers[i]['username'])) {
        targetIndex = i;
        break;
      }
    }

    // If no unfollowed streamer found from current position, search from the beginning
    if (targetIndex == -1) {
      for (int i = 0;
          i < _currentAutoFollowIndex && i < _streamers.length;
          i++) {
        if (!following.contains(_streamers[i]['username'])) {
          targetIndex = i;
          break;
        }
      }
    }

    // Navigate to the found unfollowed streamer
    if (targetIndex != -1) {
      _pageController.animateToPage(
        targetIndex,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }



  void getStreamers() async {
    var result =
        await Dio().get("https://synthrexlabs.com/twgrow/getRandomCampaigns");
    for (var item in result.data["data"]) {
      if (!following.contains(item["UserName"]) &&
          (loginBox.get("loginname") ?? "") != item["UserName"] &&
          _streamers
              .where(
                (element) => element["username"] == item["UserName"],
              )
              .isEmpty) {
        _streamers.add({
          'username': item["UserName"],
          'id': item["UserId"],
          'avatarUrl': item["ProfileImage"],
          'CID': item["CID"]
        });
      }
    }
    setState(() {});
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _twitchClient.dispose();
    super.dispose();
  }

  Future<void> _initTwitchClient() async {
    _twitchClient = TwitchClient(loginBox.get("token") ?? "");
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      // Set auto follow to false when returning to app
      if (_autoFollowEnabled) {
        setState(() {
          _autoFollowEnabled = false;
          _isAutoFollowActive = false;
        });
        // Hide overlay
        _overlayService.hideOverlay();
      }

      // Always check visited channels when app is resumed (including after being closed)
      _checkVisitedChannels();

      // Handle pending follow verification for manual follows
      if (_pendingFollowIndex != null && !_autoFollowEnabled) {
        _verifyFollowStatus(_pendingFollowIndex!);
      }

      // Skip to next unfollowed channel when app is resumed
      _navigateToNextUnfollowed();
    }
  }

  final loginBox = Hive.box("User");

  Future<void> _verifyFollowStatus(int index) async {
    if (!mounted) return;

    final streamer = _streamers[index];

    try {
      String currentUserId = loginBox.get("userid") ?? "";

      final isFollowing = await _twitchClient.checkFollowsChannel(
        currentUserId,
        (streamer['id']).toString(),
      );
      if (isFollowing && mounted) {
        await _handleSuccessfulFollow(index);
        _pendingFollowIndex = null;
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('follow.verify_failed'.tr()),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }





  Future<void> _handleFollow(int index, BuildContext context) async {
    if (_isFollowing || _isAddingCoins) return;

    // Check if user is logged in with Twitch
    String? token = loginBox.get("token");
    if (token == null || token.isEmpty) {
      _showTwitchLoginDialog(context, index);
      return;
    }

    setState(() {
      _isFollowing = true;
      _pendingFollowIndex = index;
    });

    final streamer = _streamers[index];
    final twitchUrl = 'twitch://stream/${streamer['username']}';
    final webUrl = 'https://twitch.tv/${streamer['username']}';

    try {
      if (await canLaunchUrl(Uri.parse(twitchUrl))) {
        await launchUrl(Uri.parse(twitchUrl));
      } else {
        await launchUrl(Uri.parse(webUrl));
      }
    } catch (e) {
      debugPrint('Error launching URL: $e');
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('follow.launch_failed'.tr()),
            backgroundColor: Colors.red,
          ),
        );
      }
      _pendingFollowIndex = null;
    } finally {
      if (mounted) {
        setState(() {
          _isFollowing = false;
        });
      }
    }
  }

  Future<void> _handleSuccessfulFollow(int index) async {
    following.add(_streamers[index]['username']);
    followBox.put("Follow", following);
    setState(() {
      _isAddingCoins = true;
    });

    if (!mounted) return;

    final provider = Provider.of<Data>(context, listen: false);
    await Dio().get(
        "https://synthrexlabs.com/twgrow/done/${_streamers[index]["CID"]}");

    await provider.addPoints(15);

    setState(() {
      _isAddingCoins = false;
    });
    if (!mounted) return;
    _showRewardSnackbar(context);

    // Update follow bonus progress
    _updateFollowBonusProgress();

    _seeOther();
  }

  void _showRewardSnackbar(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.monetization_on, color: Color(0xFF9146FF)),
            const SizedBox(width: 8),
            Expanded(
              child: Text('follow.coins_earned'.tr(args: ['15'])),
            )
          ],
        ),
        backgroundColor: const Color(0xFF282842),
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }

  void _seeOther() {
    _pageController.nextPage(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  Future<void> _claimWelcomeBonus() async {
    if (_isClaimingBonus) return;

    setState(() {
      _isClaimingBonus = true;
    });

    try {
      final provider = Provider.of<Data>(context, listen: false);
      await provider.addPoints(50);

      // Mark bonus as claimed
      await loginBox.put("welcomeBonusClaimed", true);

      setState(() {
        _showWelcomeBonus = false;
        _isClaimingBonus = false;
      });

      if (!mounted) return;

      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.celebration, color: Color(0xFF9146FF)),
              const SizedBox(width: 8),
              Expanded(
                child: Text('welcome_bonus.claimed_success'.tr(args: ['50'])),
              ),
            ],
          ),
          backgroundColor: const Color(0xFF282842),
          duration: const Duration(seconds: 3),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      );

      // Check if user hasn't rated and show rating dialog
      _checkAndShowRatingDialogAfterWelcomeBonus();
    } catch (e) {
      setState(() {
        _isClaimingBonus = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('welcome_bonus.claim_failed'.tr()),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _checkAndShowRatingDialogAfterWelcomeBonus() async {
    final bool hasRated = loginBox.get("hasRated") ?? false;

    if (!hasRated) {
      Future.delayed(const Duration(milliseconds: 1500), () {
        if (mounted) {
          showDialog(
            context: context,
            builder: (context) => RateAppDialog(
              onRated: () async {
                await loginBox.put("hasRated", true);
              },
            ),
          );
        }
      });
    }
  }

  Future<void> _processVisitedChannels(List<String> visitedChannels) async {
    try {
      String currentUserId = loginBox.get("userid") ?? "";
      if (currentUserId.isEmpty) return;

      final provider = Provider.of<Data>(context, listen: false);
      int coinsEarned = 0;

      for (String username in visitedChannels) {
        // Find the streamer in our list
        final streamerIndex = _streamers.indexWhere((s) => s['username'] == username);
        if (streamerIndex == -1) continue;

        final streamer = _streamers[streamerIndex];

        try {
          // Check if user followed this channel
          final isFollowing = await _twitchClient.checkFollowsChannel(
            currentUserId,
            (streamer['id']).toString(),
          );

          if (isFollowing && !following.contains(username)) {
            // Add to following list
            following.add(username);
            followBox.put("Follow", following);

            // Grant coins
            await Dio().get("https://synthrexlabs.com/twgrow/done/${streamer["CID"]}");
            await provider.addPoints(15);
            coinsEarned += 15;

            // Update follow bonus progress
            _updateFollowBonusProgress();
          }
        } catch (e) {
          debugPrint('Error checking follow status for $username: $e');
        }
      }

      if (coinsEarned > 0 && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.monetization_on, color: Color(0xFF9146FF)),
                const SizedBox(width: 8),
                Expanded(
                  child: Text('follow.coins_earned'.tr(args: [coinsEarned.toString()])),
                ),
              ],
            ),
            backgroundColor: const Color(0xFF282842),
            duration: const Duration(seconds: 3),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    } catch (e) {
      debugPrint('Error processing visited channels: $e');
    }
  }

  void _showOverlayPermissionDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          constraints: const BoxConstraints(maxWidth: 400),
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [Color(0xFF282842), Color(0xFF32324D)],
            ),
            borderRadius: BorderRadius.circular(24),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.4),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Padding(
            padding: const EdgeInsets.all(28),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Icon
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      colors: [Color(0xFF9146FF), Color(0xFF7C3AFA)],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: const Color(0xFF9146FF).withValues(alpha: 0.4),
                        blurRadius: 16,
                        offset: const Offset(0, 8),
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.auto_awesome,
                    size: 40,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 24),
                Text(
                  'overlay.permission_title'.tr(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                Text(
                  'overlay.permission_description'.tr(),
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.8),
                    fontSize: 16,
                    height: 1.4,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                // Buttons
                Row(
                  children: [
                    Expanded(
                      child: Container(
                        height: 52,
                        decoration: BoxDecoration(
                          color: Colors.transparent,
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: Colors.white.withValues(alpha: 0.2),
                            width: 1,
                          ),
                        ),
                        child: Material(
                          color: Colors.transparent,
                          child: InkWell(
                            onTap: () => Navigator.pop(context),
                            borderRadius: BorderRadius.circular(16),
                            child: Center(
                              child: Text(
                                'overlay.cancel'.tr(),
                                style: TextStyle(
                                  color: Colors.white.withValues(alpha: 0.7),
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Container(
                        height: 52,
                        decoration: BoxDecoration(
                          gradient: const LinearGradient(
                            colors: [Color(0xFF9146FF), Color(0xFF7C3AFA)],
                            begin: Alignment.centerLeft,
                            end: Alignment.centerRight,
                          ),
                          borderRadius: BorderRadius.circular(16),
                          boxShadow: [
                            BoxShadow(
                              color: const Color(0xFF9146FF).withValues(alpha: 0.4),
                              blurRadius: 12,
                              offset: const Offset(0, 6),
                            ),
                          ],
                        ),
                        child: Material(
                          color: Colors.transparent,
                          child: InkWell(
                            onTap: () async {
                              Navigator.pop(context);
                              await _overlayService.requestOverlayPermission();
                              // Check permission again after request
                              Future.delayed(const Duration(seconds: 1), () async {
                                final hasPermission = await _overlayService.checkOverlayPermission();
                                if (hasPermission) {
                                  setState(() {
                                    _autoFollowEnabled = true;
                                  });
                                  _startAutoFollow();
                                }
                              });
                            },
                            borderRadius: BorderRadius.circular(16),
                            child: Center(
                              child: Text(
                                'overlay.allow_overlay'.tr(),
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showTwitchLoginDialog(BuildContext context, int index) {
    showDialog(
      context: context,
      barrierColor: Colors.black.withValues(alpha: 0.8),
      builder: (BuildContext context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          // margin: const EdgeInsets.symmetric(horizontal: 20),
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [Color(0xFF282842), Color(0xFF32324D)],
            ),
            borderRadius: BorderRadius.circular(24),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.4),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Padding(
            padding: const EdgeInsets.all(28),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Icon with gradient background
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      colors: [Color(0xFF9146FF), Color(0xFF7C3AFA)],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: const Color(0xFF9146FF).withValues(alpha: 0.4),
                        blurRadius: 16,
                        offset: const Offset(0, 8),
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.login_rounded,
                    size: 40,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 24),

                // Title
                Text(
                  'follow.connect_with_twitch'.tr(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    letterSpacing: -0.5,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 12),

                // Description
                Text(
                  'follow.login_description'.tr(),
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.8),
                    fontSize: 16,
                    height: 1.4,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),

                // // Benefits
                // Container(
                //   padding: const EdgeInsets.all(16),
                //   decoration: BoxDecoration(
                //     color: const Color(0xFF9146FF).withValues(alpha: 0.1),
                //     borderRadius: BorderRadius.circular(12),
                //     border: Border.all(
                //       color: const Color(0xFF9146FF).withValues(alpha: 0.2),
                //       width: 1,
                //     ),
                //   ),
                //   child: Row(
                //     children: [
                //       const Icon(
                //         Icons.monetization_on,
                //         color: Color(0xFF9146FF),
                //         size: 20,
                //       ),
                //       const SizedBox(width: 8),
                //       Text(
                //         'Earn +15 coins per follow',
                //         style: TextStyle(
                //           color: Colors.white.withValues(alpha: 0.9),
                //           fontSize: 14,
                //           fontWeight: FontWeight.w500,
                //         ),
                //       ),
                //     ],
                //   ),
                // ),

                const SizedBox(height: 16),

                // Buttons
                Column(
                  children: [
                    // Login button
                    Container(
                      width: double.infinity,
                      height: 52,
                      decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          colors: [Color(0xFF9146FF), Color(0xFF7C3AFA)],
                          begin: Alignment.centerLeft,
                          end: Alignment.centerRight,
                        ),
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color:
                                const Color(0xFF9146FF).withValues(alpha: 0.4),
                            blurRadius: 12,
                            offset: const Offset(0, 6),
                          ),
                        ],
                      ),
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          onTap: () async {
                            Navigator.pop(context);
                            await _handleTwitchLogin(context, index);
                          },
                          borderRadius: BorderRadius.circular(16),
                          child: Center(
                            child: Text(
                              'follow.login_with_twitch'.tr(),
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 12),

                    // Cancel button
                    Container(
                      width: double.infinity,
                      height: 52,
                      decoration: BoxDecoration(
                        color: Colors.transparent,
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: Colors.white.withValues(alpha: 0.2),
                          width: 1,
                        ),
                      ),
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          onTap: () => Navigator.pop(context),
                          borderRadius: BorderRadius.circular(16),
                          child: Center(
                            child: Text(
                              'follow.maybe_later'.tr(),
                              style: TextStyle(
                                color: Colors.white.withValues(alpha: 0.7),
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _handleTwitchLogin(BuildContext context, int index) async {
    try {
      var token = await Navigator.of(context).push(MaterialPageRoute(
        builder: (context) => const TwitchLoginScreen(),
      ));

      if (token is String) {
        var result = await TwitchClient(token).getCurrentUser();

        loginBox.put("userid", result.id);
        loginBox.put("displayname", result.displayName);
        loginBox.put("image", result.profileImageUrl);
        loginBox.put("loginname", result.login);
        loginBox.put("month", DateTime.now().month);
        loginBox.put("token", token);
        // Reinitialize Twitch client with new token

        _twitchClient.dispose();
        _twitchClient = TwitchClient(token);

        // Update auto follow widget state since user is now logged in
        _checkAutoFollowWidget();

        // Check and show follow bonus widget for newly logged in user
        _checkFollowBonusForTwitchUser();

        // Now proceed with the follow action
        if (!context.mounted) return;
        _handleFollow(index, context);
      }
    } catch (e) {
      if (!context.mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('auth.login_failed'.tr()),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF0E0E1A),
      body: SafeArea(
        child: Column(
          children: [
            // Auto follow widget for Twitch logged users, welcome bonus for others
            if (_showAutoFollowWidget)
              _buildAutoFollowWidget()
            else if (_showWelcomeBonus)
              _buildWelcomeBonusWidget(),
            // Follow bonus widget for Twitch logged users
            if (_showFollowBonusWidget) _buildFollowBonusWidget(),
            // Main content
            Expanded(
              child: _isCheckingVisitedChannels
                  ? _buildCheckingChannelsWidget()
                  : PageView.builder(
                      controller: _pageController,
                      itemCount: _streamers.length,
                      physics: _isFollowing || _isAddingCoins
                          ? const NeverScrollableScrollPhysics()
                          : const AlwaysScrollableScrollPhysics(),
                      itemBuilder: (context, index) {
                        return _buildProfileCard(_streamers[index], index);
                      },
                    ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileCard(Map<String, dynamic> streamer, int index) {
    // Adjust margins based on whether widgets are shown
    final bool hasTopWidget =
        _showWelcomeBonus || _showAutoFollowWidget || _showFollowBonusWidget;
    final double topMargin = hasTopWidget ? 10 : 20;
    final double bottomMargin = hasTopWidget ? 10 : 20;

    return Container(
      margin: EdgeInsets.fromLTRB(20, topMargin, 20, bottomMargin),
      decoration: BoxDecoration(
        color: const Color(0xFF282842),
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.3),
            blurRadius: 16,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Center(
        child: SingleChildScrollView(
          padding: const EdgeInsets.symmetric(vertical: 16),
          physics: const BouncingScrollPhysics(),
          child: Column(
            children: [
              Container(
                width: 125,
                height: 125,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: const Color(0xFF343450),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.3),
                      blurRadius: 16,
                      offset: const Offset(0, 8),
                    ),
                  ],
                ),
                child: ClipOval(
                  child: Image.network(
                    streamer['avatarUrl'],
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return const Icon(Icons.person,
                          size: 100, color: Colors.white54);
                    },
                  ),
                ),
              ),
              SizedBox(height: 20),
              Text(
                streamer['username'],
                style: TextStyle(
                  color: Colors.white,
                  fontSize: hasTopWidget ? 24 : 28,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 12),
              if (_isFollowing || _isAddingCoins)
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: const Color(0xFF9146FF).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Color(0xFF9146FF),
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        _isFollowing
                            ? 'follow.following'.tr()
                            : 'follow.adding_coins'.tr(),
                        style: const TextStyle(
                          color: Color(0xFF9146FF),
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                )
              else
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: const Color(0xFF9146FF).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.monetization_on,
                        size: 20,
                        color: Color(0xFF9146FF),
                      ),
                      SizedBox(width: 4),
                      Text(
                        'follow.coins_reward'.tr(),
                        style: const TextStyle(
                          color: Color(0xFF9146FF),
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              SizedBox(height: 24),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 15),
                child: following.contains(streamer['username'])
                    ? // Show only "See Other" for followed channels
                    SizedBox(
                        width: double.infinity,
                        height: 50,
                        child: OutlinedButton(
                          onPressed: (_isFollowing || _isAddingCoins)
                              ? null
                              : _seeOther,
                          style: OutlinedButton.styleFrom(
                            side: BorderSide(
                              color: (_isFollowing || _isAddingCoins)
                                  ? const Color(0xFF9146FF)
                                      .withValues(alpha: 0.5)
                                  : const Color(0xFF9146FF),
                              width: 2,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(25),
                            ),
                          ),
                          child: Text(
                            'follow.see_other'.tr(),
                            style: TextStyle(
                              color: (_isFollowing || _isAddingCoins)
                                  ? const Color(0xFF9146FF)
                                      .withValues(alpha: 0.5)
                                  : const Color(0xFF9146FF),
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      )
                    : // Show both buttons in a row for unfollowed channels
                    Row(
                        children: [
                          Expanded(
                            child: SizedBox(
                              height: 50,
                              child: OutlinedButton(
                                onPressed: (_isFollowing || _isAddingCoins)
                                    ? null
                                    : _seeOther,
                                style: OutlinedButton.styleFrom(
                                  side: BorderSide(
                                    color: (_isFollowing || _isAddingCoins)
                                        ? const Color(0xFF9146FF)
                                            .withValues(alpha: 0.5)
                                        : const Color(0xFF9146FF),
                                    width: 2,
                                  ),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(25),
                                  ),
                                ),
                                child: Text(
                                  'follow.see_other'.tr(),
                                  style: TextStyle(
                                    color: (_isFollowing || _isAddingCoins)
                                        ? const Color(0xFF9146FF)
                                            .withValues(alpha: 0.5)
                                        : const Color(0xFF9146FF),
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: SizedBox(
                              height: 50,
                              child: ElevatedButton(
                                onPressed: (_isFollowing || _isAddingCoins)
                                    ? null
                                    : () => _handleFollow(index, context),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: const Color(0xFF9146FF),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(25),
                                  ),
                                ),
                                child: _isFollowing || _isAddingCoins
                                    ? const SizedBox(
                                        width: 20,
                                        height: 20,
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2,
                                          valueColor:
                                              AlwaysStoppedAnimation<Color>(
                                                  Colors.white),
                                        ),
                                      )
                                    : Text(
                                        'follow.follow'.tr(),
                                        style: const TextStyle(
                                          color: Colors.white,
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                              ),
                            ),
                          ),
                        ],
                      ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildWelcomeBonusWidget() {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 500),
      margin: const EdgeInsets.fromLTRB(16, 16, 16, 4),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFF9146FF), Color(0xFF7C3AFA)],
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF9146FF).withValues(alpha: 0.3),
            blurRadius: 16,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.celebration,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'welcome_bonus.title'.tr(),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      'welcome_bonus.description'.tr(args: ['50']),
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.9),
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _isClaimingBonus ? null : _claimWelcomeBonus,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.white,
                foregroundColor: const Color(0xFF9146FF),
                padding: const EdgeInsets.symmetric(vertical: 10),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
                elevation: 0,
              ),
              child: _isClaimingBonus
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          Color(0xFF9146FF),
                        ),
                      ),
                    )
                  : Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.monetization_on,
                          size: 16,
                        ),
                        const SizedBox(width: 6),
                        Text(
                          'welcome_bonus.claim_button'.tr(args: ['50']),
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAutoFollowWidget() {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 500),
      margin: const EdgeInsets.fromLTRB(16, 16, 16, 4),
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFF9146FF), Color(0xFF7C3AFA)],
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF9146FF).withValues(alpha: 0.3),
            blurRadius: 16,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.auto_mode,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'auto_follow.title'.tr(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Switch(
                value: _autoFollowEnabled,
                onChanged: _toggleAutoFollow,
                activeColor: Colors.white,
                activeTrackColor: Colors.white.withValues(alpha: 0.3),
                inactiveThumbColor: Colors.white.withValues(alpha: 0.7),
                inactiveTrackColor: Colors.white.withValues(alpha: 0.2),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFollowBonusWidget() {
    final double progress = _followBonusProgress / 30.0;

    return AnimatedContainer(
      duration: const Duration(milliseconds: 500),
      margin: const EdgeInsets.fromLTRB(16, 4, 16, 4),
      padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFF00C851), Color(0xFF00A843)],
        ),
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF00C851).withValues(alpha: 0.3),
            blurRadius: 9,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              // Container(
              //   padding: const EdgeInsets.all(8),
              //   decoration: BoxDecoration(
              //     color: Colors.white.withValues(alpha: 0.2),
              //     borderRadius: BorderRadius.circular(8),
              //   ),
              //   child: const Icon(
              //     Icons.star,
              //     color: Colors.white,
              //     size: 20,
              //   ),
              // ),
              // const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'follow_bonus.title'.tr(),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    // Progress bar
                    Row(
                      children: [
                        Text(
                          '$_followBonusProgress/30',
                          style: TextStyle(
                            color: Colors.white.withValues(alpha: 0.9),
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        SizedBox(
                          width: 6,
                        ),
                        Expanded(
                          child: Container(
                            width: double.infinity,
                            height: 8,
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: FractionallySizedBox(
                              alignment: Alignment.centerLeft,
                              widthFactor: progress,
                              child: Container(
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(4),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 12),
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(
                      Icons.monetization_on,
                      color: Colors.white,
                      size: 16,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '100',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }



  Widget _buildCheckingChannelsWidget() {
    return Container(
      margin: const EdgeInsets.fromLTRB(20, 20, 20, 20),
      decoration: BoxDecoration(
        color: const Color(0xFF282842),
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.3),
            blurRadius: 16,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Center(
        child: Padding(
          padding: const EdgeInsets.all(40),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Color(0xFF9146FF), Color(0xFF7C3AFA)],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF9146FF).withValues(alpha: 0.4),
                      blurRadius: 16,
                      offset: const Offset(0, 8),
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.search,
                  size: 40,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 24),
              Text(
                'Checking channels and adding coins',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 12),
              Text(
                'Checking ${_visitedChannels.length} channels...',
                style: TextStyle(
                  color: Colors.white.withValues(alpha: 0.7),
                  fontSize: 16,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              const SizedBox(
                width: 40,
                height: 40,
                child: CircularProgressIndicator(
                  strokeWidth: 3,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    Color(0xFF9146FF),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
